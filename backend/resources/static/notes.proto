syntax = "proto3";

message Note {
    int32 pitch = 1;
    float start_time = 2;
    float duration = 3;
    float velocity = 4;

    int32 page = 5;
    int32 track = 6;
    repeated int32 bbox = 7;
    int32 confidence = 8;
    int32 id = 9;
}

message NoteList {
    repeated Note notes = 1;
    repeated int32 size = 2;
    repeated Voice voices = 3;
    repeated Line lines = 4;
}

enum EditOperation {
    INSERT = 0;
    SUBSTITUTE = 1;
    DELETE = 2;
}

enum Clef {
    TREBLE = 0;
    BASS = 1;
    UNKNOWN = 2;
}

message Edit {
    EditOperation operation = 1;
    int32 pos = 2;
    Note s_char = 3;  // Source character (for DELETE and SUBSTITUTE)
    Note t_char = 4;  // Target character (for INSERT and SUBSTITUTE)
    int32 t_pos = 5;  // Target position (for SUBSTITUTE)
}

message Voice {
    Clef clef = 1;
    int32 track = 2;
    int32 group = 3;
    repeated int32 bbox = 4;
    int32 page = 5;
}

message Line {
    repeated Clef clefs = 1;
    int32 group = 2;
    repeated int32 bbox = 3;
    int32 page = 4;
}

message TempoSection {
    int32 start_index = 1;
    int32 end_index = 2;
    float tempo = 3;
}

message ScoringResult {
    repeated Edit edits = 1;
    repeated int32 size = 2;
    float unstable_rate = 3;
    repeated TempoSection tempo_sections = 4;
}
