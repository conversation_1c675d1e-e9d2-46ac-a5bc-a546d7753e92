# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: notes.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import builder as _builder
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x0bnotes.proto\"\x98\x01\n\x04Note\x12\r\n\x05pitch\x18\x01 \x01(\x05\x12\x12\n\nstart_time\x18\x02 \x01(\x02\x12\x10\n\x08\x64uration\x18\x03 \x01(\x02\x12\x10\n\x08velocity\x18\x04 \x01(\x02\x12\x0c\n\x04page\x18\x05 \x01(\x05\x12\r\n\x05track\x18\x06 \x01(\x05\x12\x0c\n\x04\x62\x62ox\x18\x07 \x03(\x05\x12\x12\n\nconfidence\x18\x08 \x01(\x05\x12\n\n\x02id\x18\t \x01(\x05\"\\\n\x08NoteList\x12\x14\n\x05notes\x18\x01 \x03(\x0b\x32\x05.Note\x12\x0c\n\x04size\x18\x02 \x03(\x05\x12\x16\n\x06voices\x18\x03 \x03(\x0b\x32\x06.Voice\x12\x14\n\x05lines\x18\x04 \x03(\x0b\x32\x05.Line\"s\n\x04\x45\x64it\x12!\n\toperation\x18\x01 \x01(\x0e\x32\x0e.EditOperation\x12\x0b\n\x03pos\x18\x02 \x01(\x05\x12\x15\n\x06s_char\x18\x03 \x01(\x0b\x32\x05.Note\x12\x15\n\x06t_char\x18\x04 \x01(\x0b\x32\x05.Note\x12\r\n\x05t_pos\x18\x05 \x01(\x05\"V\n\x05Voice\x12\x13\n\x04\x63lef\x18\x01 \x01(\x0e\x32\x05.Clef\x12\r\n\x05track\x18\x02 \x01(\x05\x12\r\n\x05group\x18\x03 \x01(\x05\x12\x0c\n\x04\x62\x62ox\x18\x04 \x03(\x05\x12\x0c\n\x04page\x18\x05 \x01(\x05\"G\n\x04Line\x12\x14\n\x05\x63lefs\x18\x01 \x03(\x0e\x32\x05.Clef\x12\r\n\x05group\x18\x02 \x01(\x05\x12\x0c\n\x04\x62\x62ox\x18\x03 \x03(\x05\x12\x0c\n\x04page\x18\x04 \x01(\x05\"E\n\x0cTempoSection\x12\x13\n\x0bstart_index\x18\x01 \x01(\x05\x12\x11\n\tend_index\x18\x02 \x01(\x05\x12\r\n\x05tempo\x18\x03 \x01(\x02\"q\n\rScoringResult\x12\x14\n\x05\x65\x64its\x18\x01 \x03(\x0b\x32\x05.Edit\x12\x0c\n\x04size\x18\x02 \x03(\x05\x12\x15\n\runstable_rate\x18\x03 \x01(\x02\x12%\n\x0etempo_sections\x18\x04 \x03(\x0b\x32\r.TempoSection*7\n\rEditOperation\x12\n\n\x06INSERT\x10\x00\x12\x0e\n\nSUBSTITUTE\x10\x01\x12\n\n\x06\x44\x45LETE\x10\x02*)\n\x04\x43lef\x12\n\n\x06TREBLE\x10\x00\x12\x08\n\x04\x42\x41SS\x10\x01\x12\x0b\n\x07UNKNOWN\x10\x02\x62\x06proto3')

_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, globals())
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'notes_pb2', globals())
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _EDITOPERATION._serialized_start=728
  _EDITOPERATION._serialized_end=783
  _CLEF._serialized_start=785
  _CLEF._serialized_end=826
  _NOTE._serialized_start=16
  _NOTE._serialized_end=168
  _NOTELIST._serialized_start=170
  _NOTELIST._serialized_end=262
  _EDIT._serialized_start=264
  _EDIT._serialized_end=379
  _VOICE._serialized_start=381
  _VOICE._serialized_end=467
  _LINE._serialized_start=469
  _LINE._serialized_end=540
  _TEMPOSECTION._serialized_start=542
  _TEMPOSECTION._serialized_end=611
  _SCORINGRESULT._serialized_start=613
  _SCORINGRESULT._serialized_end=726
# @@protoc_insertion_point(module_scope)
