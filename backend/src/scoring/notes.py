# Generated by the protocol buffer compiler.  DO NOT EDIT!
# sources: notes.proto
# plugin: python-betterproto
from dataclasses import dataclass
from typing import List

import betterproto


class EditOperation(betterproto.Enum):
    INSERT = 0
    SUBSTITUTE = 1
    DELETE = 2


class Clef(betterproto.Enum):
    TREBLE = 0
    BASS = 1
    UNKNOWN = 2


@dataclass
class Note(betterproto.Message):
    pitch: int = betterproto.int32_field(1)
    start_time: float = betterproto.float_field(2)
    duration: float = betterproto.float_field(3)
    velocity: float = betterproto.float_field(4)
    page: int = betterproto.int32_field(5)
    track: int = betterproto.int32_field(6)
    bbox: List[int] = betterproto.int32_field(7)
    confidence: int = betterproto.int32_field(8)
    id: int = betterproto.int32_field(9)


@dataclass
class NoteList(betterproto.Message):
    notes: List["Note"] = betterproto.message_field(1)
    size: List[int] = betterproto.int32_field(2)
    voices: List["Voice"] = betterproto.message_field(3)
    lines: List["Line"] = betterproto.message_field(4)


@dataclass
class Edit(betterproto.Message):
    operation: "EditOperation" = betterproto.enum_field(1)
    pos: int = betterproto.int32_field(2)
    s_char: "Note" = betterproto.message_field(3)
    t_char: "Note" = betterproto.message_field(4)
    t_pos: int = betterproto.int32_field(5)


@dataclass
class Voice(betterproto.Message):
    clef: "Clef" = betterproto.enum_field(1)
    track: int = betterproto.int32_field(2)
    group: int = betterproto.int32_field(3)
    bbox: List[int] = betterproto.int32_field(4)
    page: int = betterproto.int32_field(5)


@dataclass
class Line(betterproto.Message):
    clefs: List["Clef"] = betterproto.enum_field(1)
    group: int = betterproto.int32_field(2)
    bbox: List[int] = betterproto.int32_field(3)
    page: int = betterproto.int32_field(4)


@dataclass
class TempoSection(betterproto.Message):
    start_index: int = betterproto.int32_field(1)
    end_index: int = betterproto.int32_field(2)
    tempo: float = betterproto.float_field(3)


@dataclass
class ScoringResult(betterproto.Message):
    edits: List["Edit"] = betterproto.message_field(1)
    size: List[int] = betterproto.int32_field(2)
    unstable_rate: float = betterproto.float_field(3)
    tempo_sections: List["TempoSection"] = betterproto.message_field(4)
