# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.
# ---Project--- #
!.idea/runConfigurations/

# ---Backend--- #
backend/resources/debug_info
.env

# ---Frontend--- #

# dependencies
node_modules
.pnp
.pnp.js
.yarn/install-state.gz

# testing
coverage

# next.js
.next/
# production
# misc
.DS_Store
# debug
npm-debug.log*
yarn-debug.log*
# local env files
.env*.local

# vercel
.vercel

# typescript
next-env.d.ts

certificates
eslint.config.mjs
next.config.mjs
postcss.config.mjs
global-setup.js
