import React, {
  RefObject,
  useContext,
  useEffect,
  useRef,
  useState,
} from "react";
import { ZoomContext } from "@/app/providers";
import { usePinch, useWheel } from "@use-gesture/react";

export default function ZoomableDiv({
  children,
  recenter,
  onScaleChange,
  defaultScale = 1,
}: {
  children: React.ReactNode;
  recenter: RefObject<HTMLButtonElement>;
  onScaleChange?: (scale: number) => void;
  defaultScale?: number;
}) {
  const outerRef = useRef<HTMLDivElement>(null);
  const innerRef = useRef<HTMLDivElement>(null);
  const [scale, setScale] = useState(defaultScale);
  // Track the previous scale so we can compute relative changes.
  const prevScaleRef = useRef(defaultScale);
  const zoomSensitivity = 0.0015;
  const minScale = 0.25;
  const maxScale = 4;

  // Get zoom context
  const zoomContext = useContext(ZoomContext);
  // Keep track of the score ID
  const scoreIdRef = useRef<string>("");

  // Extract scoreId from parent element's ID
  useEffect(() => {
    if (outerRef.current) {
      const parentElement = outerRef.current.closest('[id^="score-"]');
      if (parentElement) {
        const id = parentElement.id;
        scoreIdRef.current = id.replace("score-", "");
      }
    }
  }, []);

  // Update scale when defaultScale changes
  useEffect(() => {
    setScale(defaultScale);
    prevScaleRef.current = defaultScale;
  }, [defaultScale]);

  // Utility to clamp scale value.
  const clamp = (val: number, min: number, max: number) =>
    Math.min(Math.max(val, min), max);

  // Store the original (unscaled) dimensions of the inner container.
  const [originalWidth, setOriginalWidth] = useState<number | null>(null);
  const [originalHeight, setOriginalHeight] = useState<number | null>(null);

  // On mount, capture the original dimensions and set width to the minimum of the current width and (screen width - 6px)
  useEffect(() => {
    if (innerRef.current) {
      const currentWidth = innerRef.current.offsetWidth;
      const screenWidth = window.innerWidth - 6;
      setOriginalWidth(Math.min(currentWidth, screenWidth));
      setOriginalHeight(innerRef.current.offsetHeight);
    }
  }, []);

  // Zoom handlers
  useWheel(
    ({ event, delta: [, dy] }) => {
      if (!(event.ctrlKey || event.metaKey)) return;
      event.preventDefault();
      const newScale = clamp(scale - dy * zoomSensitivity, minScale, maxScale);
      setScale(newScale);
      if (zoomContext && scoreIdRef.current) {
        zoomContext.setZoomLevel(scoreIdRef.current, newScale);
      }
    },
    { target: outerRef, eventOptions: { passive: false } },
  );

  usePinch(
    ({ event, offset: [d] }) => {
      event.preventDefault();
      const newScale = clamp(d, minScale, maxScale);
      setScale(newScale);
      if (zoomContext && scoreIdRef.current) {
        zoomContext.setZoomLevel(scoreIdRef.current, newScale);
      }
    },
    {
      target: outerRef,
      eventOptions: { passive: false },
      pinch: {
        scaleBounds: { min: minScale, max: maxScale },
        from: () => [scale, 0],
      },
    },
  );

  useEffect(() => {
    const handleRecenter = () => {
      const resetScale = 1;
      setScale(resetScale);
      if (zoomContext && scoreIdRef.current) {
        zoomContext.setZoomLevel(scoreIdRef.current, resetScale);
      }
    };

    const btn = recenter.current;
    btn?.addEventListener("click", handleRecenter);
    return () => {
      btn?.removeEventListener("click", handleRecenter);
    };
  }, [recenter, zoomContext]);

  // When scale changes, update the transform, recalc dimensions, and adjust scroll so that the center remains.
  useEffect(() => {
    if (
      !innerRef.current ||
      !outerRef.current ||
      originalWidth === null ||
      originalHeight === null
    )
      return;
    const outer = outerRef.current;
    const inner = innerRef.current;

    // Capture the vertical center of the visible area.
    const containerHeight = outer.clientHeight;
    const currentScrollTop = outer.scrollTop;
    const centerY = currentScrollTop + containerHeight / 2;

    // Apply the scaling transform with origin at the top-left.
    inner.style.transform = `scale(${scale})`;
    inner.style.transformOrigin = "0 0";

    // Calculate new effective dimensions.
    const newWidth = originalWidth * scale;
    const newHeight = originalHeight * scale;

    // Update inner container's size so that the scrollable area reflects the new dimensions.
    inner.style.width = `${newWidth}px`;
    inner.style.height = `${newHeight}px`;

    // Compute the new center by applying the scale change relative to the previous scale.
    const prevScale = prevScaleRef.current;
    const newCenterY = centerY * (scale / prevScale);

    // Adjust scrollTop so that the content center remains constant.
    outer.scrollTop = newCenterY - containerHeight / 2;

    // Update previous scale.
    prevScaleRef.current = scale;

    // Update zoom context if we have a scoreId
    if (zoomContext && scoreIdRef.current) {
      zoomContext.setZoomLevel(scoreIdRef.current, scale);
    }

    // Notify parent component about scale change if the callback is provided
    if (onScaleChange) {
      onScaleChange(scale);
    }
  }, [scale, originalWidth, originalHeight, onScaleChange, zoomContext]);

  return (
    <div
      ref={outerRef}
      className="relative h-full overflow-x-hidden overflow-y-auto zoomable-div"
      data-scale={scale}
    >
      <div ref={innerRef} className="zoomable-content">
        {children}
      </div>
    </div>
  );
}
