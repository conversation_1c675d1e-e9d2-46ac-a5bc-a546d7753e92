{"name": "note", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack --experimental-https", "build": "next build", "start": "next start", "lint": "eslint .", "test": "NODE_ENV=test DEBUG=\"msw:*\" playwright test", "clean:deps": "npx depcheck --oneline | xargs -r npm uninstall && npm prune"}, "dependencies": {"@eslint/eslintrc": "^3.3.1", "@hcaptcha/react-hcaptcha": "latest", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-slider": "^1.3.2", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-tooltip": "^1.1.8", "@tanstack/query-sync-storage-persister": "latest", "@tanstack/react-query": "^5.66.3", "@use-gesture/react": "^10.3.1", "appwrite": "latest", "axios": "^1.7.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.475.0", "next": "^15.4.0-canary.115", "next-themes": "^0.4.4", "opensheetmusicdisplay": "latest", "protobufjs": "^7.4.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-toastify": "^11.0.3", "recordrtc": "^5.6.2", "tailwind-merge": "^3.0.1", "tailwindcss-animate": "^1.0.7", "webrtc-adapter": "^9.0.3"}, "devDependencies": {"@next/env": "^15.4.0-canary.115", "@playwright/test": "^1.51.1", "@types/react": "^18.3.20", "@types/react-dom": "^18.3.6", "@types/recordrtc": "^5.6.14", "@typescript-eslint/eslint-plugin": "^8.35.1", "@typescript-eslint/parser": "^8.35.1", "eslint": "^8.57.1", "eslint-config-next": "latest", "eslint-plugin-tsc": "^2.0.0", "msw": "^2.3.0", "tailwindcss": "^3.4.1", "typescript": "^5"}}