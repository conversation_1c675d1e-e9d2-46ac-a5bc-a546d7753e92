# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.
# ---Project--- #
.idea
!.idea/runConfigurations/

# ---Backend--- #
backend/resources/debug_info
.env
.env.prod

# ---Frontend--- #

# dependencies
node_modules
.pnp
.pnp.js
.yarn/install-state.gz

# testing
coverage
playwright-report/
test-results/

# next.js
.next/
out/

# production
build

# misc
.DS_Store
*.pem
eslint.config.mjs

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env*.local

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

certificates
